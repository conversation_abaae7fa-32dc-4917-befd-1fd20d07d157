<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn Sales Navigator UI Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f3f6f8;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-actions {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
        }
        .test-btn {
            background: #0a66c2;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
        }
        .test-btn:hover {
            background: #004182;
        }
        .test-btn.secondary {
            background: #6c757d;
        }
        .test-btn.secondary:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>LinkedIn Sales Navigator UI Test</h1>
            <p>This page simulates the LinkedIn Sales Navigator environment to test the Send Connect and Failed Connect buttons.</p>
            
            <div class="test-actions">
                <button class="test-btn" onclick="initializeUI()">Initialize Sales Navigator UI</button>
                <button class="test-btn secondary" onclick="addTestProfiles()">Add Test Profiles</button>
                <button class="test-btn secondary" onclick="clearProfiles()">Clear All Profiles</button>
            </div>
        </div>
    </div>

    <!-- Mock Chrome Extension API -->
    <script>
        // Mock chrome.runtime for testing
        window.chrome = {
            runtime: {
                getURL: (path) => path,
                sendMessage: (message) => {
                    console.log('Mock chrome.runtime.sendMessage:', message);
                }
            }
        };

        // Mock LinkedIn URL detection
        Object.defineProperty(window, 'location', {
            value: {
                href: 'https://www.linkedin.com/sales/search/people?viewAllFilters=true',
                hostname: 'www.linkedin.com'
            },
            writable: true
        });

        let salesNavUI = null;

        function initializeUI() {
            if (salesNavUI) {
                console.log('UI already initialized');
                return;
            }

            // Load the sales navigator UI script
            const script = document.createElement('script');
            script.src = 'content/sales-navigator-ui.js';
            script.onload = () => {
                console.log('Sales Navigator UI script loaded');
                // The script will auto-initialize
                setTimeout(() => {
                    salesNavUI = window.salesNavUI;
                    if (salesNavUI) {
                        console.log('Sales Navigator UI initialized successfully');
                    }
                }, 1000);
            };
            document.head.appendChild(script);
        }

        function addTestProfiles() {
            if (!salesNavUI) {
                alert('Please initialize the UI first');
                return;
            }

            const testProfiles = [
                {
                    name: 'John Smith',
                    title: 'Software Engineer',
                    company: 'Tech Corp',
                    location: 'San Francisco, CA',
                    url: 'https://www.linkedin.com/in/johnsmith/',
                    profilePic: '',
                    timestamp: Date.now(),
                    source: 'test'
                },
                {
                    name: 'Sarah Johnson',
                    title: 'Product Manager',
                    company: 'Innovation Inc',
                    location: 'New York, NY',
                    url: 'https://www.linkedin.com/in/sarahjohnson/',
                    profilePic: '',
                    timestamp: Date.now(),
                    source: 'test'
                },
                {
                    name: 'Mike Davis',
                    title: 'Marketing Director',
                    company: 'Growth Solutions',
                    location: 'Austin, TX',
                    url: 'https://www.linkedin.com/in/mikedavis/',
                    profilePic: '',
                    timestamp: Date.now(),
                    source: 'test'
                }
            ];

            testProfiles.forEach(profile => {
                salesNavUI.addProfile(profile);
            });

            console.log('Added test profiles');
        }

        function clearProfiles() {
            if (!salesNavUI) {
                alert('Please initialize the UI first');
                return;
            }

            salesNavUI.clearProfiles();
            console.log('Cleared all profiles');
        }

        // Auto-initialize on page load
        window.addEventListener('load', () => {
            setTimeout(initializeUI, 1000);
        });
    </script>
</body>
</html>
